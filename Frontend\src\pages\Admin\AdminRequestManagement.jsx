import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectRequests,
  selectSelectedRequests,
  selectRequestFilters,
  selectRequestsPagination,
  selectLoading,
  selectErrors,
  setSelectedRequests,
  setRequestFilters,
  showRequestDetailModal,
} from "../../redux/slices/adminDashboardSlice";
import {
  fetchRequests,
  deleteRequest,
  bulkDeleteRequests,
  getRequestStats,
  updateRequestStatus,
} from "../../redux/slices/adminDashboardThunks";
import AdminLayout from "../../components/admin/AdminLayout";
import Table from "../../components/common/Table";
import "../../styles/AdminRequestManagement.css";
import { FaSearch, FaFilter, FaEye, FaEdit, FaTrash, FaCheck, FaTimes, FaDollarSign, FaCalendarAlt, FaUser } from "react-icons/fa";

const AdminRequestManagement = () => {
    const dispatch = useDispatch();

    // Redux state
    const requests = useSelector(selectRequests);
    const selectedRequests = useSelector(selectSelectedRequests);
    const filters = useSelector(selectRequestFilters);
    const pagination = useSelector(selectRequestsPagination);
    const loading = useSelector(selectLoading);
    const errors = useSelector(selectErrors);

    // Local state
    const [searchTerm, setSearchTerm] = useState(filters.search || "");
    const [statusFilter, setStatusFilter] = useState(filters.status || "all");

    // Fetch requests on component mount and when filters change
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            dispatch(fetchRequests({
                page: pagination.current,
                limit: pagination.limit,
                search: searchTerm,
                status: statusFilter === 'all' ? '' : statusFilter,
                sortBy: filters.sortBy,
                sortOrder: filters.sortOrder
            }));
        }, 300); // Debounce search

        return () => clearTimeout(timeoutId);
    }, [dispatch, pagination.current, pagination.limit, searchTerm, statusFilter, filters.sortBy, filters.sortOrder]);

    // Initial fetch
    useEffect(() => {
        dispatch(getRequestStats());
    }, [dispatch]);

    // Use server-side filtered data directly
    const displayRequests = requests.data || [];

    // Handle search with filter update
    const handleSearchChange = (value) => {
        setSearchTerm(value);
        dispatch(setRequestFilters({ search: value, page: 1 }));
    };

    // Handle status filter change
    const handleStatusFilterChange = (value) => {
        setStatusFilter(value);
        dispatch(setRequestFilters({ status: value === 'all' ? '' : value, page: 1 }));
    };

    // Handle select all
    const handleSelectAll = (e) => {
        if (e.target.checked) {
            dispatch(setSelectedRequests(displayRequests.map((request) => request._id)));
        } else {
            dispatch(setSelectedRequests([]));
        }
    };

    // Handle individual select
    const handleSelectRequest = (requestId) => {
        const newSelection = selectedRequests.includes(requestId)
            ? selectedRequests.filter((id) => id !== requestId)
            : [...selectedRequests, requestId];
        dispatch(setSelectedRequests(newSelection));
    };

    // Format currency
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    // Format date
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    // Get status badge
    const getStatusBadge = (status) => {
        const statusClasses = {
            'Pending': 'status-pending',
            'Accepted': 'status-accepted',
            'Rejected': 'status-rejected',
            'Completed': 'status-completed',
            'Cancelled': 'status-cancelled'
        };
        return <span className={`status-badge ${statusClasses[status] || 'status-default'}`}>{status}</span>;
    };

    // Table columns configuration
    const tableColumns = [
        {
            key: 'select',
            label: (
                <input
                    type="checkbox"
                    onChange={handleSelectAll}
                    checked={
                        selectedRequests.length === displayRequests.length &&
                        displayRequests.length > 0
                    }
                />
            ),
            render: (request) => (
                <input
                    type="checkbox"
                    checked={selectedRequests.includes(request._id)}
                    onChange={() => handleSelectRequest(request._id)}
                />
            ),
        },
        {
            key: 'id',
            label: 'Request ID',
            render: (request) => `REQ-${request._id.slice(-8).toUpperCase()}`,
        },
        {
            key: 'title',
            label: 'Title',
            render: (request) => (
                <div className="request-title">
                    <span className="title">{request.title}</span>
                    <span className="content-type">{request.contentType}</span>
                </div>
            ),
        },
        {
            key: 'buyer',
            label: 'Buyer',
            render: (request) => (
                <div className="user-info">
                    <span className="name">
                        {request.buyer?.firstName} {request.buyer?.lastName}
                    </span>
                    <span className="email">{request.buyer?.email}</span>
                </div>
            ),
        },
        {
            key: 'sport',
            label: 'Sport',
            render: (request) => request.sport,
        },
        {
            key: 'budget',
            label: 'Budget',
            render: (request) => formatCurrency(request.budget),
        },
        {
            key: 'status',
            label: 'Status',
            render: (request) => getStatusBadge(request.status),
        },
        {
            key: 'createdAt',
            label: 'Created Date',
            render: (request) => formatDate(request.createdAt),
        },
        {
            key: 'actions',
            label: 'Actions',
            render: (request) => (
                <div className="action-buttons">
                    <button
                        className="btn btn-sm btn-outline"
                        onClick={() => dispatch(showRequestDetailModal(request))}
                        title="View Details"
                    >
                        <FaEye />
                    </button>
                    {request.status === 'Pending' && (
                        <>
                            <button
                                className="btn btn-sm btn-success"
                                onClick={() => handleUpdateStatus(request._id, 'Accepted')}
                                title="Accept Request"
                            >
                                <FaCheck />
                            </button>
                            <button
                                className="btn btn-sm btn-danger"
                                onClick={() => handleUpdateStatus(request._id, 'Rejected')}
                                title="Reject Request"
                            >
                                <FaTimes />
                            </button>
                        </>
                    )}
                </div>
            ),
        },
    ];

    // Handle status update
    const handleUpdateStatus = async (requestId, status) => {
        try {
            await dispatch(updateRequestStatus({
                id: requestId,
                data: { status }
            })).unwrap();
            // Refresh the list
            dispatch(fetchRequests({
                page: pagination.current,
                limit: pagination.limit,
                search: searchTerm,
                status: statusFilter === 'all' ? '' : statusFilter,
                sortBy: filters.sortBy,
                sortOrder: filters.sortOrder
            }));
        } catch (error) {
            console.error('Failed to update request status:', error);
        }
    };

    return (
        <AdminLayout>
            <div className="AdminRequestManagement">
                <div className="AdminRequestManagement__header">
                    <h2>Request Management</h2>
                    <p>Manage and monitor all custom training requests</p>
                </div>

                {/* Search and Filter Section */}
                <div className="AdminRequestManagement__controls">
                    <div className="search-box">
                        <FaSearch className="search-icon" />
                        <input
                            type="text"
                            placeholder="Search requests..."
                            value={searchTerm}
                            onChange={(e) => handleSearchChange(e.target.value)}
                        />
                    </div>

                    <div className="filter-box">
                        <select
                            value={statusFilter}
                            onChange={(e) => handleStatusFilterChange(e.target.value)}
                        >
                            <option value="all">All Status</option>
                            <option value="Pending">Pending</option>
                            <option value="Accepted">Accepted</option>
                            <option value="Rejected">Rejected</option>
                            <option value="Completed">Completed</option>
                            <option value="Cancelled">Cancelled</option>
                        </select>
                    </div>
                </div>

                {/* Table Section */}
                <div className="AdminRequestManagement__table">
                    <Table
                        columns={tableColumns}
                        data={displayRequests}
                        isAdmin={true}
                        loading={{
                            isLoading: loading.requests,
                            message: "Loading requests...",
                        }}
                        emptyMessage="No requests found"
                        className="requests-table"
                    />
                </div>

                {/* Pagination */}
                <div className="AdminRequestManagement__pagination">
                    <div className="pagination-info">
                        Showing {(pagination.current - 1) * pagination.limit + 1} to {Math.min(pagination.current * pagination.limit, pagination.total)} of {pagination.total} requests
                    </div>
                    <div className="pagination-controls">
                        <button
                            className="btn btn-outline"
                            disabled={pagination.current <= 1 || loading.requests}
                            onClick={() => dispatch(setRequestFilters({ page: pagination.current - 1 }))}
                        >
                            Previous
                        </button>
                        <span className="page-number active">{pagination.current}</span>
                        <span className="page-info">of {pagination.pages}</span>
                        <button
                            className="btn btn-outline"
                            disabled={pagination.current >= pagination.pages || loading.requests}
                            onClick={() => dispatch(setRequestFilters({ page: pagination.current + 1 }))}
                        >
                            Next
                        </button>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
};

export default AdminRequestManagement; 